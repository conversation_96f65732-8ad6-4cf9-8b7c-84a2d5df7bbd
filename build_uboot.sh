#!/bin/bash

# K230 CANMV U-Boot编译脚本

set -e  # 遇到错误立即退出

echo "开始编译K230 CANMV U-Boot..."

# 进入U-Boot目录
cd src/uboot/uboot

# 清理之前的编译结果
echo "清理之前的编译结果..."
make clean

# 配置编译目标
echo "配置编译目标为k230_canmv..."
make k230_canmv_defconfig

# 编译U-Boot
echo "开始编译U-Boot..."
make -j$(nproc)

# 检查编译结果
if [ -f "u-boot.bin" ]; then
    echo "✅ U-Boot编译成功！"
    echo "生成的文件："
    ls -la u-boot.bin
    echo "文件大小: $(du -h u-boot.bin | cut -f1)"
else
    echo "❌ U-Boot编译失败！"
    exit 1
fi

echo "编译完成！"
