# K230 CANMV IO20 公放使能配置说明

## 概述

本文档说明如何在K230 CANMV开发板上配置IO20引脚作为公放使能信号，实现开机自动使能公放功能。

## 硬件连接

- **IO20引脚**: 对应GPIO20，用作公放使能信号
- **功能**: 输出高电平使能公放，输出低电平禁用公放
- **电压**: 3.3V逻辑电平

## 软件配置

### 1. U-Boot配置

在U-Boot阶段，IO20已经在以下文件中配置：

#### 设备树配置 (`src/uboot/uboot/arch/riscv/dts/k230_canmv.dts`)
```dts
// SPEAKER_AMP_EN (公放使能)
(IO20) ( 0<<SEL | 0<<SL | BANK_VOLTAGE_IO14_IO25<<MSC | 1<<IE | 1<<OE | 0<<PU | 0<<PD | 15<<DS | 1<<ST )
```

#### 板级初始化 (`src/uboot/uboot/board/kendryte/k230_canmv/board.c`)
```c
// Configure IO20 (GPIO20) as speaker amplifier enable
// GPIO20 is in GPIO0 bank (pins 0-31), so use GPIO_BASE_ADDR0
u32 speaker_amp_gpio20_dir = readl((void *)(GPIO_BASE_ADDR0 + 0x4));
speaker_amp_gpio20_dir |= 1 << 20;  // Set GPIO20 as output
writel(speaker_amp_gpio20_dir, (void *)(GPIO_BASE_ADDR0 + 0x4));

// Set GPIO20 high to enable speaker amplifier
u32 speaker_amp_gpio20_data = readl((void *)(GPIO_BASE_ADDR0 + 0x0));
speaker_amp_gpio20_data |= 1 << 20;  // Set GPIO20 high
writel(speaker_amp_gpio20_data, (void *)(GPIO_BASE_ADDR0 + 0x0));
```

### 2. RT-Smart配置

在RT-Smart系统中，IO20的pinmux配置在以下文件中：

#### Pinmux配置 (`src/rtsmart/rtsmart/kernel/bsp/maix3/board/configs/k230_canmv/pinmux_config.c`)
```c
[20] = { .u.bit = { .st = 1, .ds = 15, .pd = 0, .pu = 0, .oe = 1, .ie = 1, .msc = VOL_BANK1_IO14_25, .io_sel = 0 } }, // GPIO20
```

### 3. 应用程序控制

提供了示例应用程序来控制公放使能：

#### 示例代码 (`src/rtsmart/examples/speaker_amp_control.c`)

该示例提供了以下功能：
- 初始化GPIO20
- 使能/禁用公放
- 查询公放状态
- 开机自动使能公放

## 使用方法

### 1. 编译和烧录

1. 编译U-Boot和RT-Smart固件
2. 烧录到K230 CANMV开发板
3. 开机后IO20会自动配置为高电平，使能公放

### 2. 运行时控制

在RT-Smart系统中，可以使用以下命令控制公放：

```bash
# 初始化公放GPIO
speaker_amp_cmd init

# 使能公放
speaker_amp_cmd enable

# 禁用公放
speaker_amp_cmd disable

# 查询公放状态
speaker_amp_cmd status

# 运行测试程序
speaker_amp_test_cmd
```

### 3. 编程接口

在应用程序中可以使用以下函数：

```c
// 初始化公放GPIO
int speaker_amp_gpio_init(void);

// 使能公放
int speaker_amp_enable(void);

// 禁用公放
int speaker_amp_disable(void);

// 获取公放状态
int speaker_amp_get_status(void);
```

## 技术细节

### GPIO寄存器地址
- **GPIO0基地址**: 0x9140B000
- **数据寄存器偏移**: 0x0
- **方向寄存器偏移**: 0x4
- **GPIO20位位置**: bit 20

### 寄存器操作
```c
#define GPIO_BASE_ADDR0     (0x9140B000U)

// 设置GPIO20为输出
u32 dir_reg = readl((void *)(GPIO_BASE_ADDR0 + 0x4));
dir_reg |= (1 << 20);
writel(dir_reg, (void *)(GPIO_BASE_ADDR0 + 0x4));

// 设置GPIO20输出高电平
u32 data_reg = readl((void *)(GPIO_BASE_ADDR0 + 0x0));
data_reg |= (1 << 20);
writel(data_reg, (void *)(GPIO_BASE_ADDR0 + 0x0));
```

## 启动时序

1. **U-Boot阶段**: 
   - 配置IO20的pinmux为GPIO功能
   - 设置GPIO20为输出模式
   - 输出高电平使能公放

2. **RT-Smart阶段**:
   - 保持pinmux配置
   - 应用程序可以通过GPIO驱动控制公放

## 注意事项

1. **电压兼容性**: 确保公放模块的使能信号与3.3V逻辑电平兼容
2. **电流限制**: GPIO20的驱动能力有限，如需驱动大电流负载请使用缓冲器
3. **时序要求**: 公放使能后可能需要一定的稳定时间才能正常工作
4. **音频路径**: 确保音频信号路径正确连接到公放输入

## 故障排除

### 公放无声音
1. 检查IO20电平是否为高
2. 检查音频信号路径
3. 检查公放电源供应
4. 检查音频编解码器配置

### GPIO控制失败
1. 检查GPIO驱动是否正确加载
2. 检查pinmux配置是否正确
3. 检查寄存器读写权限

## 相关文件

- `src/uboot/uboot/arch/riscv/dts/k230_canmv.dts` - U-Boot设备树配置
- `src/uboot/uboot/board/kendryte/k230_canmv/board.c` - U-Boot板级初始化
- `src/rtsmart/rtsmart/kernel/bsp/maix3/board/configs/k230_canmv/pinmux_config.c` - RT-Smart pinmux配置
- `src/rtsmart/examples/speaker_amp_control.c` - 公放控制示例程序
