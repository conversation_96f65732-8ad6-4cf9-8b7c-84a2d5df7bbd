/*
 * SPDX-License-Identifier: BSD-2-Clause
 *
 * Copyright (c) 2019 Western Digital Corporation or its affiliates.
 *
 * Authors: <AUTHORS>
 */

/dts-v1/;
/ {
	#address-cells = <2>;
	#size-cells = <2>;
	compatible = "kendryte,k210";

	chosen {
                bootargs = "console=hvc0 earlycon=sbi";
	};

	cpus {
		#address-cells = <1>;
		#size-cells = <0>;
		cpu0: cpu@0 {
			device_type = "cpu";
			clock-frequency = <*********>;
			i-cache-size = <32768>;
			d-cache-size = <32768>;
			mmu-type = "none";
			reg = <0>;
			riscv,isa = "rv64imafdc";
			status = "okay";
			cpu0_intc: interrupt-controller {
				#interrupt-cells = <1>;
				compatible = "riscv,cpu-intc";
				interrupt-controller;
			};
		};
		cpu1: cpu@1 {
			device_type = "cpu";
			clock-frequency = <*********>;
			d-cache-size = <32768>;
			i-cache-size = <32768>;
			mmu-type = "none";
			reg = <1>;
			riscv,isa = "rv64imafdc";
			status = "okay";
			cpu1_intc: interrupt-controller {
				#interrupt-cells = <1>;
				compatible = "riscv,cpu-intc";
				interrupt-controller;
			};
		};
	};

	memory@******** {
		/* Bank 0: 4 MB, Bank 1: 2 MB, AI chip SRAM: 2MB */
		device_type = "memory";
		reg = <0x00000000 0x******** 0x00000000 0x00800000>;
	};

	plic0: interrupt-controller@C000000 {
		#interrupt-cells = <1>;
		compatible = "riscv,plic0";
		interrupt-controller;
		interrupts-extended =
			<&cpu0_intc 11 &cpu0_intc 9
			 &cpu1_intc 11 &cpu1_intc 9>;
		reg = <0x0 0xc000000 0x0 0x4000000>;
	};
};
