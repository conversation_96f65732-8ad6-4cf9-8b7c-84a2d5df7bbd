/* Copyright (c) 2025, Canaan Bright Sight Co., Ltd
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 * 1. Redistributions of source code must retain the above copyright
 * notice, this list of conditions and the following disclaimer.
 * 2. Redistributions in binary form must reproduce the above copyright
 * notice, this list of conditions and the following disclaimer in the
 * documentation and/or other materials provided with the distribution.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND
 * CONTRIBUTORS "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES,
 * INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES OF
 * MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE
 * DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL,
 * SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING,
 * BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR
 * SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
 * NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE
 * OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

/**
 * @file speaker_amp_control.c
 * @brief K230 CANMV Speaker Amplifier Control Example
 * 
 * This example demonstrates how to control the speaker amplifier enable pin (IO20/GPIO20)
 * on the K230 CANMV board. The speaker amplifier is controlled via GPIO20.
 */

#include <rtthread.h>
#include <rtdevice.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

// GPIO20 is used for speaker amplifier enable
#define SPEAKER_AMP_GPIO_PIN    20

// GPIO device handle
static rt_device_t gpio_dev = RT_NULL;

/**
 * @brief Initialize speaker amplifier GPIO
 * @return 0 on success, negative on error
 */
static int speaker_amp_gpio_init(void)
{
    // Find GPIO device
    gpio_dev = rt_device_find("gpio");
    if (gpio_dev == RT_NULL) {
        rt_kprintf("Failed to find GPIO device\n");
        return -1;
    }

    // Open GPIO device
    if (rt_device_open(gpio_dev, RT_DEVICE_OFLAG_RDWR) != RT_EOK) {
        rt_kprintf("Failed to open GPIO device\n");
        return -1;
    }

    rt_kprintf("Speaker amplifier GPIO initialized successfully\n");
    return 0;
}

/**
 * @brief Enable speaker amplifier
 * @return 0 on success, negative on error
 */
static int speaker_amp_enable(void)
{
    rt_uint8_t value = 1;
    rt_off_t pos = SPEAKER_AMP_GPIO_PIN;

    if (gpio_dev == RT_NULL) {
        rt_kprintf("GPIO device not initialized\n");
        return -1;
    }

    // Set GPIO20 to high to enable speaker amplifier
    if (rt_device_write(gpio_dev, pos, &value, 1) != 1) {
        rt_kprintf("Failed to enable speaker amplifier\n");
        return -1;
    }

    rt_kprintf("Speaker amplifier enabled\n");
    return 0;
}

/**
 * @brief Disable speaker amplifier
 * @return 0 on success, negative on error
 */
static int speaker_amp_disable(void)
{
    rt_uint8_t value = 0;
    rt_off_t pos = SPEAKER_AMP_GPIO_PIN;

    if (gpio_dev == RT_NULL) {
        rt_kprintf("GPIO device not initialized\n");
        return -1;
    }

    // Set GPIO20 to low to disable speaker amplifier
    if (rt_device_write(gpio_dev, pos, &value, 1) != 1) {
        rt_kprintf("Failed to disable speaker amplifier\n");
        return -1;
    }

    rt_kprintf("Speaker amplifier disabled\n");
    return 0;
}

/**
 * @brief Get speaker amplifier status
 * @return 1 if enabled, 0 if disabled, negative on error
 */
static int speaker_amp_get_status(void)
{
    rt_uint8_t value = 0;
    rt_off_t pos = SPEAKER_AMP_GPIO_PIN;

    if (gpio_dev == RT_NULL) {
        rt_kprintf("GPIO device not initialized\n");
        return -1;
    }

    // Read GPIO20 status
    if (rt_device_read(gpio_dev, pos, &value, 1) != 1) {
        rt_kprintf("Failed to read speaker amplifier status\n");
        return -1;
    }

    return value;
}

/**
 * @brief Speaker amplifier control command
 */
static void speaker_amp_cmd(int argc, char **argv)
{
    if (argc < 2) {
        rt_kprintf("Usage: speaker_amp <init|enable|disable|status>\n");
        rt_kprintf("  init   - Initialize speaker amplifier GPIO\n");
        rt_kprintf("  enable - Enable speaker amplifier\n");
        rt_kprintf("  disable- Disable speaker amplifier\n");
        rt_kprintf("  status - Get speaker amplifier status\n");
        return;
    }

    if (strcmp(argv[1], "init") == 0) {
        speaker_amp_gpio_init();
    } else if (strcmp(argv[1], "enable") == 0) {
        speaker_amp_enable();
    } else if (strcmp(argv[1], "disable") == 0) {
        speaker_amp_disable();
    } else if (strcmp(argv[1], "status") == 0) {
        int status = speaker_amp_get_status();
        if (status >= 0) {
            rt_kprintf("Speaker amplifier is %s\n", status ? "enabled" : "disabled");
        }
    } else {
        rt_kprintf("Unknown command: %s\n", argv[1]);
        rt_kprintf("Usage: speaker_amp <init|enable|disable|status>\n");
    }
}

/**
 * @brief Speaker amplifier test thread
 */
static void speaker_amp_test_thread(void *parameter)
{
    int ret;

    rt_kprintf("Starting speaker amplifier test...\n");

    // Initialize GPIO
    ret = speaker_amp_gpio_init();
    if (ret != 0) {
        rt_kprintf("Failed to initialize speaker amplifier GPIO\n");
        return;
    }

    // Test enable/disable cycle
    for (int i = 0; i < 3; i++) {
        rt_kprintf("Test cycle %d:\n", i + 1);
        
        // Enable speaker amplifier
        speaker_amp_enable();
        rt_thread_mdelay(2000);  // Wait 2 seconds
        
        // Check status
        int status = speaker_amp_get_status();
        rt_kprintf("Status after enable: %s\n", status ? "enabled" : "disabled");
        
        // Disable speaker amplifier
        speaker_amp_disable();
        rt_thread_mdelay(2000);  // Wait 2 seconds
        
        // Check status
        status = speaker_amp_get_status();
        rt_kprintf("Status after disable: %s\n", status ? "enabled" : "disabled");
        
        rt_kprintf("\n");
    }

    rt_kprintf("Speaker amplifier test completed\n");
}

/**
 * @brief Start speaker amplifier test
 */
static void speaker_amp_test_cmd(int argc, char **argv)
{
    rt_thread_t tid;

    tid = rt_thread_create("spk_test", speaker_amp_test_thread, RT_NULL,
                          2048, RT_THREAD_PRIORITY_MAX / 2, 20);
    if (tid != RT_NULL) {
        rt_thread_startup(tid);
        rt_kprintf("Speaker amplifier test started\n");
    } else {
        rt_kprintf("Failed to create speaker amplifier test thread\n");
    }
}

// Register MSH commands
MSH_CMD_EXPORT(speaker_amp_cmd, Speaker amplifier control);
MSH_CMD_EXPORT(speaker_amp_test_cmd, Speaker amplifier test);

/**
 * @brief Auto-enable speaker amplifier on boot
 * This function will be called during system initialization
 */
static int speaker_amp_auto_init(void)
{
    // Wait a bit for system to stabilize
    rt_thread_mdelay(1000);
    
    // Initialize and enable speaker amplifier
    if (speaker_amp_gpio_init() == 0) {
        speaker_amp_enable();
        rt_kprintf("Speaker amplifier auto-enabled on boot\n");
    }
    
    return 0;
}

// Auto-initialize speaker amplifier during system startup
INIT_APP_EXPORT(speaker_amp_auto_init);
