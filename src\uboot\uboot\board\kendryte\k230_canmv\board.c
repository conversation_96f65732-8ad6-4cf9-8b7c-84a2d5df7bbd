#include <asm/asm.h>
#include <asm/io.h>
#include <asm/spl.h>
#include <asm/types.h>
#include <command.h>
#include <common.h>
#include <cpu_func.h>
#include <dm.h>
#include <env_internal.h>
#include <image.h>
#include <linux/delay.h>
#include <linux/kernel.h>
#include <lmb.h>
#include <stdio.h>

#include <kendryte/k230_platform.h>

#include "board_common.h"

int ddr_init_training(void) {
  if (0x00 != (readl((const volatile void __iomem *)0x980001bcULL) & 0x1)) {
    // have init ,not need reinit;
    return 0;
  }

  board_ddr_init();

  return 0;
}

int board_early_init_f(void) {
  /* force set boot medium to sdio1 */
  g_boot_medium = BOOT_MEDIUM_SDIO1;

  printf("board_early_init_f: Configuring GPIO20 for speaker amplifier\n");

  // Configure IO20 (GPIO20) as speaker amplifier enable
#define GPIO_BASE_ADDR0     (0x9140B000U)

  // Set GPIO20 as output
  u32 gpio_dir = readl((void *)(GPIO_BASE_ADDR0 + 0x4));
  gpio_dir |= 1 << 20;  // Set GPIO20 as output
  writel(gpio_dir, (void *)(GPIO_BASE_ADDR0 + 0x4));

  // Set GPIO20 high to enable speaker amplifier
  u32 gpio_data = readl((void *)(GPIO_BASE_ADDR0 + 0x0));
  gpio_data |= 1 << 20;  // Set GPIO20 high
  writel(gpio_data, (void *)(GPIO_BASE_ADDR0 + 0x0));

  printf("Speaker amplifier enabled via GPIO20 in early init\n");

  return 0;
}

#ifdef CONFIG_BOARD_LATE_INIT
int board_late_init(void) {
  printf("board_late_init: Starting initialization\n");

  // Configure WiFi REGON (GPIO1)
  u32 wifi_regon_gpio1_dir = readl((void *)(GPIO_BASE_ADDR0 + 0x4));
  wifi_regon_gpio1_dir |= 1 << 1;
  writel(wifi_regon_gpio1_dir, (void *)(GPIO_BASE_ADDR0 + 0x4));

  // reset gpio1 -> WIFI REGON
  u32 wifi_regon_gpio1_data = readl((void *)(GPIO_BASE_ADDR0 + 0x0));
  wifi_regon_gpio1_data &= ~(1 << 1);
  writel(wifi_regon_gpio1_data, (void *)(GPIO_BASE_ADDR0 + 0x0));
  mdelay(10);
  // reset gpio1 -> WIFI REGON
  wifi_regon_gpio1_data |= 1 << 1;
  writel(wifi_regon_gpio1_data, (void *)(GPIO_BASE_ADDR0 + 0x0));
  printf("WiFi REGON configured via GPIO1\n");

  // Configure IO20 (GPIO20) as speaker amplifier enable
  // GPIO20 is in GPIO0 bank (pins 0-31), so use GPIO_BASE_ADDR0
  u32 speaker_amp_gpio20_dir = readl((void *)(GPIO_BASE_ADDR0 + 0x4));
  speaker_amp_gpio20_dir |= 1 << 20;  // Set GPIO20 as output
  writel(speaker_amp_gpio20_dir, (void *)(GPIO_BASE_ADDR0 + 0x4));

  // Set GPIO20 high to enable speaker amplifier
  u32 speaker_amp_gpio20_data = readl((void *)(GPIO_BASE_ADDR0 + 0x0));
  speaker_amp_gpio20_data |= 1 << 20;  // Set GPIO20 high
  writel(speaker_amp_gpio20_data, (void *)(GPIO_BASE_ADDR0 + 0x0));

  printf("Speaker amplifier enabled via GPIO20 in late init\n");

  printf("board_late_init: Completed\n");
  return 0;
}
#endif
